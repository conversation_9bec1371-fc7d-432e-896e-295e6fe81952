import 'dart:convert';

import 'package:wu_llm/core/reader_base.dart';

import '../core/waite_tarot.dart';

part 'reader_divine.dart';
part 'reader_decision.dart';
part 'reader_journal.dart';
part 'reader_rating.dart';

enum TarotReaderMode {
  divine("占卜"),
  decision("決策"),
  journal("日誌"),
  rating("評分");

  final String name;
  const TarotReaderMode(this.name);

  static TarotReaderMode byName(String? name) {
    return values.firstWhere((e) => e.name == name, orElse: () => TarotReaderMode.divine);
  }
}

class WaiteCard {
  String cardId = "";
  bool reversed = false;
  String? title;
  int rating = 0;
  String? reading;

  WaiteCard({required this.cardId, required this.reversed});

  String get fullname {
    try {
      final cardEntry = WaiteTarot.cards.entries.firstWhere((entry) => entry.key == cardId);
      final card = cardEntry.value;
      var ret = card.name;
      if (reversed) {
        ret += "(reversed)";
      }
      return ret;
    } catch (e) {
      // Handle the case where cardId doesn't match any card name
      // Perhaps return a default string or the cardId itself
      return reversed ? "$cardId (reversed)" : cardId;
    }
  }
}

ReaderBase getWaiteReader(TarotReaderMode mode, String question, List<WaiteCard> cards) {
  return switch (mode) {
    TarotReaderMode.divine => ReaderDivine(question, cards),
    TarotReaderMode.decision => ReaderDecision(question, cards),
    TarotReaderMode.journal => ReaderJournal(question, cards),
    TarotReaderMode.rating => ReaderRating(question, cards),
  };
}
