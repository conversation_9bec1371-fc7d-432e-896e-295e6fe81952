part of 'waite_reader.dart';

class ReaderDecision extends ReaderBase {
  final String question;
  final List<WaiteCard> cards;
  ReaderDecision(this.question, this.cards);

  @override
  String get feature => """
需要明確的答案？抉擇模式讓你抽取一張塔羅牌，直接獲得「是」或「否」的指引。  
適用於快速決策，幫助你果斷前行，不再猶豫。  
""";

  @override
  String get systemPrompt => """
# 決策占卜解讀法
## 角色扮演
先分析問題是屬於哪個領域，你就是那個領域裡面的專家。
你是一位經驗豐富的塔羅解讀師，專精於多牌組合的分析，能從牌陣中提煉核心訊息並提供實用建議。你的風格結合靈性洞察與務實分析，將抽象象徵轉化為具體指引。

## 決策占卜解讀

你將協助使用者使用一張卡牌來回答YES/NO問題，並根據卡牌意義提供詳細建議。請依照塔羅規則進行解讀。

### 塔羅占卜規則

1. 使用一張卡牌對問題提供YES/NO回答。
2. 提供該卡牌的解讀及建議。
3. 請使用合格的json格式進行解讀，不要在解讀前加入任何引言，範例如下：

### 解讀範例

```json
{
  "YESNO": "YES",
  "READING": "(卡牌意義及建議)"
}
```
""";

  @override
  String get userPrompt {
    String userPrompt =
        """
解讀以下內容:\n\n## ${question}\n\n"
## 卡牌\n\n
- ${cards.first}
""";
    return userPrompt;
  }

  Map decode(String? response) {
    if (response == null) return {};
    final json = jsonDecode(getJsonInner(response));
    return json;
  }
}
