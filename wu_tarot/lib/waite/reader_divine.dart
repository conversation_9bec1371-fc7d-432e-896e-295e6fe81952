part of 'waite_reader.dart';

class ReaderDivine extends ReaderBase {
  final String question;
  final List<WaiteCard> cards;
  ReaderDivine(this.question, this.cards);

  @override
  String get feature => """
想掌握未來的方向嗎？占卜模式透過五張塔羅牌找出關鍵牌，揭示命運的奧秘。  
無論是感情、事業或人生難題，都能提供深入的指引，幫助你做出最佳選擇。  
""";

  @override
  String get systemPrompt => """
# 五張關鍵牌陣塔羅解讀法

## 角色扮演
先分析問題是屬於哪個領域，你就是那個領域裡面的專家。
你是一位經驗豐富的塔羅解讀師，擅長分析多牌組合，從五張塔羅牌中提煉核心訊息並提供實用的指引。解讀時，將牌陣分為三組：「前綴牌」代表當前情境，「關鍵牌」為核心結果，「後綴牌」顯示後續影響。根據牌種數量特徵，將提供具深度的分析與建議，並遵循以下規則：

## 塔羅解讀要點

1. 先分析問題所會涉及的人事時地物，後續的解讀會基於這些關鍵標題為基礎。
2. 若某牌種數量達三張以上，需特別關注其特性。
3. 若逆位牌較多，表示占卜問題可能不適合。
4. 首先提供簡明的摘要，目的讓使用者快速了解結果，不要用卡牌描述。
5. 請使用markdown格式進行解讀，不要在解讀前加入任何引言，不要用三引號標記。

## 當牌種數量特多時：

- **大牌（Major Arcana）多**: 暗示命運與靈性課題主導情境，事件影響深遠，需專注於內在成長。
- **權杖（火元素）多**: 顯示行動力與創新能量，需留意避免過於衝動，保持實際可行性。
- **聖杯（水元素）多**: 情感與人際關係主導，需平衡情感波動，避免幻想過度影響判斷。
- **寶劍（風元素）多**: 強調理性思考與決策，避免過度理性化或焦慮影響判斷。
- **錢幣（土元素）多**: 強調穩定與物質需求，需關注情感與靈性層面的平衡。
- **宮廷牌多**: 暗示人際互動或角色分工，需觀察每張牌的屬性並分析人際關係中的核心議題。

## 解讀範例：
```markdown
## 摘要
(整體占卜的摘要)

## 關鍵牌
- **(卡牌名)**: (卡牌意義)

## 前綴牌
- **(卡牌名1)**: (卡牌1意義)
- **(卡牌名2)**: (卡牌2意義)

## 後綴牌
- **(卡牌名1)**: (卡牌1意義)
- **(卡牌名2)**: (卡牌2意義)

## 總結及建議
(總結及建議)
```
""";

  @override
  String get userPrompt {
    // 傳入所有符合條件的牌，如果是唯一一張則傳回它在所有牌的索引
    int getIndex(List<WaiteCard> checks) => checks.length == 1 ? cards.indexOf(checks.first) : -1;

    int findKeyIndex() {
      var conditions = [
        (WaiteCard e) => e.cardId.startsWith("M") && e.reversed, // 唯一逆立大阿卡那牌
        (WaiteCard e) => e.cardId.startsWith("M") && !e.reversed, // 唯一正立大阿卡那牌
        (WaiteCard e) => e.reversed, // 唯一反轉牌
        (WaiteCard e) => !e.reversed, // 唯一正立牌
      ];
      // 依序檢查條件，找到就回傳索引
      for (var condition in conditions) {
        var index = getIndex(cards.where(condition).toList());
        if (index != -1) return index;
      }
      return cards.length ~/ 2; // 若未找到，取中間的牌
    }

    final keyIndex = findKeyIndex();
    final prefix = cards.sublist(0, keyIndex);
    final key = cards[keyIndex];
    final suffix = cards.sublist(keyIndex + 1);
    // 先解讀關鍵牌
    var ret = "\n## 關鍵牌\n";
    ret += "- ${key.fullname}\n";
    // 再解讀前綴牌
    ret += "\n## 前綴牌\n";
    if (prefix.isEmpty) {
      ret += "\n- 沒有前綴牌\n";
    } else {
      ret += prefix.map((e) => "- ${e.fullname}\n").join();
    }
    // 再解讀後綴牌
    ret += "\n## 後綴牌\n";
    if (suffix.isEmpty) {
      ret += "\n- 沒有後綴牌\n";
    } else {
      ret += suffix.map((e) => "- ${e.fullname}\n").join();
    }
    // 加入牌數統計
    ret += "\n## 牌數統計\n";
    ret += "- 大牌：${cards.where((e) => e.cardId.startsWith("M")).length}\n";
    ret += "- 权杖：${cards.where((e) => e.cardId.startsWith("W")).length}\n";
    ret += "- 圣杯：${cards.where((e) => e.cardId.startsWith("C")).length}\n";
    ret += "- 宝剑：${cards.where((e) => e.cardId.startsWith("S")).length}\n";
    ret += "- 钱币：${cards.where((e) => e.cardId.startsWith("P")).length}\n";
    ret += "- 宫廷：${cards.where((e) => e.cardId.substring(1, 2) == "C").length}\n";
    ret += "- 逆位：${cards.where((e) => e.reversed).length}\n";
    return ret;
  }
}
