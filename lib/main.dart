import 'package:adv_fortune/pages/home/<USER>';
import 'package:flex_color_scheme/flex_color_scheme.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';

import 'pages/qimen/controllers/cell_controller.dart';
import 'pages/qimen/controllers/desk_controller.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await GetStorage.init();
  Get.put(DeskController());
  Get.put(CellController());

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    const scheme = FlexScheme.bahamaBlue;
    return GetMaterialApp(
      title: "FORTUNE",
      debugShowCheckedModeBanner: false,
      themeMode: ThemeMode.light,
      theme: FlexThemeData.light(scheme: scheme),
      // darkTheme: FlexThemeData.dark(scheme: scheme),
      home: const HomePage(),
    );
  }
}
