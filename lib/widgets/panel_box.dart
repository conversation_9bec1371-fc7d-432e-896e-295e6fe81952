import 'package:flutter/material.dart';

class PanelBox extends StatelessWidget {
  final Color? backColor, borderColor;
  final EdgeInsets? margin, padding;
  final BorderRadius? borderRadius;
  final Decoration? decoration;
  final Widget? child;
  const PanelBox({
    super.key,
    this.backColor,
    this.borderColor,
    this.margin,
    this.borderRadius,
    this.decoration,
    this.child,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    final decoration = this.decoration ??
        BoxDecoration(
          color: backColor ?? colorScheme.primaryContainer,
          borderRadius: borderRadius ?? BorderRadius.circular(8.0),
          border: Border.all(color: borderColor ?? Colors.transparent),
        );
    return Padding(
      padding: margin ?? const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0),
      child: Container(
        clipBehavior: Clip.antiAlias,
        decoration: decoration,
        padding: padding ?? const EdgeInsets.all(8.0),
        child: child,
      ),
    );
  }
}
