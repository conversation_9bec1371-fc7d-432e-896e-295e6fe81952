import 'package:flutter/material.dart';
import 'package:wu_core/wu_auto_layout.dart';

final mainMenu = <MenuItem>[
  MenuItem(title: "塔羅牌資訊", normalIcon: const Icon(Icons.book)),
  MenuItem(title: "塔羅-三張牌陣", normalIcon: const Icon(Icons.book)),
  MenuItem(title: "塔羅-直覺五牌占", normalIcon: const Icon(Icons.book)),
  MenuItem(title: "塔羅-元辰宮占卜", normalIcon: const Icon(Icons.book)),
  MenuItem(title: "塔羅-單張評分牌陣", normalIcon: const Icon(Icons.book)),
  MenuItem(title: "塔羅-三張評分牌陣", normalIcon: const Icon(Icons.book)),
  MenuItem(title: "塔羅-事業進展占卜", normalIcon: const Icon(Icons.book)),
  MenuItem(title: "塔羅-財運與理財占卜", normalIcon: const Icon(Icons.book)),
  MenuItem(title: "塔羅-愛情與關係占卜", normalIcon: const Icon(Icons.book)),
  MenuItem(title: "塔羅-健康與身心平衡", normalIcon: const Icon(Icons.book)),
  MenuItem(title: "塔羅-學業與成長占卜", normalIcon: const Icon(Icons.book)),
  MenuItem(title: "塔羅-創意與靈感占卜", normalIcon: const Icon(Icons.book)),
  MenuItem(title: "塔羅-家庭與人際關係占卜", normalIcon: const Icon(Icons.book)),
  MenuItem(title: "塔羅-旅行與冒險占卜", normalIcon: const Icon(Icons.book)),
  MenuItem(title: "盧恩符文資訊", normalIcon: const Icon(Icons.book)),
  MenuItem(title: "盧恩-三張牌陣", normalIcon: const Icon(Icons.book)),
  MenuItem(title: "盧恩-單張評分牌陣", normalIcon: const Icon(Icons.book)),
  MenuItem(title: "六爻", normalIcon: const Icon(Icons.book)),
  MenuItem(title: "梅易", normalIcon: const Icon(Icons.book)),
  MenuItem(title: "易經", normalIcon: const Icon(Icons.book)),
  MenuItem(title: "數字易經", normalIcon: const Icon(Icons.book)),
  MenuItem(title: "經典小六壬", normalIcon: const Icon(Icons.book)),
  MenuItem(title: "秘傳小六壬", normalIcon: const Icon(Icons.book)),
  MenuItem(title: "奇門", normalIcon: const Icon(Icons.book)),
  MenuItem(title: "八字", normalIcon: const Icon(Icons.book)),
  MenuItem(title: "紫微", normalIcon: const Icon(Icons.book)),
  MenuItem(title: "靈籤-觀音靈籤", normalIcon: const Icon(Icons.book)),
  MenuItem(title: "靈籤-雷雨師", normalIcon: const Icon(Icons.book)),
  // MenuItem(title: "塔羅", normalIcon: const Icon(Icons.book), body: TarotModeList()),
];
