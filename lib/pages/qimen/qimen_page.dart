import 'package:adv_fortune/pages/qimen/cols/desk_col.dart';
import 'package:flutter/material.dart';

import 'cols/cell_col.dart';
import 'controllers/desk_controller.dart';

class QimenPage extends StatefulWidget {
  const QimenPage({super.key});

  @override
  State<QimenPage> createState() => _QimenPageState();
}

class _QimenPageState extends State<QimenPage> {
  @override
  void initState() {
    Get.find<DeskController>().init();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: Get.theme.scaffoldBackgroundColor,
        body: ThreeColLayout(
          left: SizedBox(width: 600, child: DeskCol()),
          center: CellCol(),
          right: Container(),
        ),
      ),
    );
  }
}

class ThreeColLayout extends StatelessWidget {
  final Widget left;
  final Widget center;
  final Widget right;
  final double? spacing;
  const ThreeColLayout({
    super.key,
    required this.left,
    required this.center,
    required this.right,
    this.spacing,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.max,
      crossAxisAlignment: CrossAxisAlignment.start,
      spacing: spacing ?? 12,
      children: [
        left,
        Expanded(flex: 1, child: center),
        Expanded(flex: 1, child: right),
      ],
    );
  }
}
