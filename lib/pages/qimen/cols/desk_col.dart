import 'package:flutter/material.dart';
import 'package:wu_qimen/widgets/qimen_header_view.dart';
import 'package:wu_qimen/wu_qimen.dart';

import '../controllers/desk_controller.dart';
import '../widgets/desk_render.dart';

class DeskCol extends StatelessWidget {
  const DeskCol({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<DeskController>(builder: (deskCtrl) {
      return Column(
        children: [
          _buildHeader(),
          QimenHeaderView(desk: deskCtrl.desk),
          DeskRender(desk: deskCtrl.desk),
        ],
      );
    });
  }

  Widget _buildHeader() {
    return GetBuilder<DeskController>(builder: (deskCtrl) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          DropdownButton<DeskMode>(
            items: DeskMode.values.map((e) => DropdownMenuItem(value: e, child: Text(e.name))).toList(),
            value: deskCtrl.deskMode,
            focusColor: Colors.transparent,
            onChanged: (value) {
              deskCtrl.deskMode = value!;
            },
          ),
          Row(
            children: [
              TextButton(
                  onPressed: () async {
                    final newDate = await Get.dialog(
                      DialogSolarPicker(solar: deskCtrl.solar),
                    );
                    if (newDate != null) {
                      deskCtrl.solar = newDate;
                    }
                  },
                  child: Text("${deskCtrl.solar.ymdhm()}")),
              TextButton(
                  onPressed: () {
                    deskCtrl.solar = DateTime.now();
                  },
                  child: Text("當下")),
            ],
          ),
          Turn(
              turn: deskCtrl.turn,
              onChanged: (value) {
                deskCtrl.turn = value % 8;
              }),
        ],
      );
    });
  }
}

class Turn extends StatelessWidget {
  final int turn;
  final Function(int turn) onChanged;
  const Turn({super.key, required this.turn, required this.onChanged});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Row(
      children: [
        IconButton(
            onPressed: () {
              onChanged(turn - 1);
            },
            icon: Icon(Icons.arrow_back_ios)),
        InkWell(
          child: Container(
            color: colorScheme.secondaryContainer,
            alignment: Alignment.center,
            width: 20,
            child: Text("$turn", style: TextStyle(color: colorScheme.onSecondaryContainer)),
          ),
          onTap: () {
            onChanged(0);
          },
        ),
        IconButton(
            onPressed: () {
              onChanged(turn + 1);
            },
            icon: Icon(Icons.arrow_forward_ios)),
      ],
    );
  }
}
