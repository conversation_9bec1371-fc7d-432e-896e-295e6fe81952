import 'package:adv_fortune/pages/qimen/controllers/cell_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

class CellCol extends StatelessWidget {
  const CellCol({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CellController>(
        init: CellController(),
        builder: (controller) {
          final cell = controller.cell;
          return MarkdownBody(data: cell.describe);
        });
  }
}
