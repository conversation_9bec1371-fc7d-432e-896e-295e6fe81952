import 'package:flutter/material.dart';
import 'package:wu_qimen/wu_qimen.dart';

class Outdoor extends StatelessWidget {
  final DateTime solar;
  final int startHour;
  final int showHours;
  const Outdoor({
    super.key,
    required this.solar,
    this.startHour = 7,
    this.showHours = 3,
  });

  @override
  Widget build(BuildContext context) {
    var rows = <TableRow>[];
    for (var hour = 0; hour < showHours; hour++) {
      var workTime = DateTime(solar.year, solar.month, solar.day, startHour + hour * 2);
      final qimen = QimenBuilder.build(deskMode: DeskMode.yangHour, solar: workTime);
      final cell = qimen.cellFirst("門", "開")!;
      rows.add(TableRow(children: [
        Text(qimen.gzdate.h.zhi.name, textAlign: TextAlign.center),
        Text(cellDirection(qimen, cell.serial), textAlign: TextAlign.center),
        Text(cellDirection(qimen, cell.serial + 1), textAlign: TextAlign.center),
        Text(cellDirection(qimen, cell.serial + 2), textAlign: TextAlign.center),
        Text(cellDirection(qimen, cell.serial + 4), textAlign: TextAlign.center),
      ]));
    }
    final colorScheme = Theme.of(context).colorScheme;
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(color: colorScheme.primary),
          width: double.infinity,
          child: Text(
            "出行訣",
            style: TextStyle(color: colorScheme.onPrimary),
            textAlign: TextAlign.center,
          ),
        ),
        Table(
          border: TableBorder.all(color: colorScheme.onPrimary),
          children: [
            TableRow(children: [
              SizedBox.shrink(),
              Text("開", textAlign: TextAlign.center),
              Text("休", textAlign: TextAlign.center),
              Text("生", textAlign: TextAlign.center),
              Text("景", textAlign: TextAlign.center)
            ]),
            ...rows
          ],
        ),
      ],
    );
  }

  String cellDirection(QimenDesk qimen, int serial) {
    final gua8 = Gua8.byName(qimen.cellSerial(serial).p["後天"]);
    return gua8.direction;
  }
}
