import 'package:flutter/material.dart';

class ItemRender extends StatelessWidget {
  final Widget child;
  final String? subtitle;
  const ItemRender({super.key, required this.child, this.subtitle});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        DefaultTextStyle(
          style: TextStyle(fontSize: 20),
          child: child,
        ),
        Text(
          subtitle ?? "",
          style: TextStyle(fontSize: 10, height: 1.0, color: Colors.grey),
        ),
      ],
    );
  }
}
