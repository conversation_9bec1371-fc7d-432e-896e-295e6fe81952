import 'package:flutter/material.dart';
import 'package:wu_qimen/wu_qimen.dart';

import '../controllers/cell_controller.dart';
import 'cell_row.dart';
import 'item_render.dart';

export 'cell_row.dart';
export 'item_render.dart';

class CellRender extends StatelessWidget {
  final QimenCell cell;
  const CellRender({
    super.key,
    required this.cell,
  });

  @override
  Widget build(BuildContext context) {
    if (cell.p["後天"] == "中") return Container(color: Colors.grey);
    final textStyle = TextStyle(color: Colors.black, fontSize: 24);
    final pikes = cell.getPikes().map((p) => Text(p, style: TextStyle(fontSize: 10))).toList();
    return GetBuilder<CellController>(builder: (cellCtrl) {
      final activeCell = Get.find<CellController>().cell == cell;
      final backColor = activeCell ? Colors.yellow.shade100 : Colors.transparent;
      return InkWell(
        onTap: () {
          cellCtrl.cell = cell;
        },
        child: Container(
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: backColor,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            spacing: 4,
            children: [
              SizedBox(height: 12, child: Row(spacing: 2, children: pikes)),
              CellRow(
                left: ItemRender(child: Text(cell.p["後天"], style: textStyle)),
                center: ItemRender(child: Text(cell.p["神"], style: textStyle)),
                right: buildMaKong(),
              ),
              CellRow(
                left: Container(),
                center: buildStar(),
                right: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    if (cell.p["寄天"] != null) buildGanItem(cell.p["寄天"]),
                    buildGanItem(cell.p["天盤"]),
                  ],
                ),
              ),
              CellRow(
                left: buildGanItem(cell.p["引干"]),
                center: buildDoor(),
                right: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    if (cell.p["寄地"] != null) buildGanItem(cell.p["寄地"]),
                    buildGanItem(cell.p["地盤"]),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget buildMaKong() {
    if (cell.serial == 0) return Container();

    return ItemRender(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (cell.p["空"] ?? false) Text("◎", style: TextStyle(fontSize: 24, color: Colors.green)),
          if (cell.p["馬"] ?? false) Text("🐎", style: TextStyle(fontSize: 24)),
        ],
      ),
    );
  }

  Widget buildStar() {
    final fb = cell.fourBad(cell.star.name);
    Color textColor = Colors.black;
    if (fb.contains('迫')) textColor = Colors.blue;
    if (fb.contains('制')) textColor = Colors.green;
    final gzdate = cell.desk.gzdate;
    final sk = ShengKe.byCompare(gzdate.d.zhi, cell.star);
    String subtitle = sk.wangxiang;
    return ItemRender(
      subtitle: subtitle,
      child: Text(cell.star.name, style: TextStyle(color: textColor, fontSize: 24)),
    );
  }

  Widget buildDoor() {
    final fb = cell.fourBad(cell.door.name);
    Color textColor = Colors.black;
    if (fb.contains('迫')) textColor = Colors.blue;
    if (fb.contains('制')) textColor = Colors.green;
    final gzdate = cell.desk.gzdate;
    final sk = ShengKe.byCompare(gzdate.d.zhi, cell.door);
    String subtitle = sk.wangxiang;
    return ItemRender(
      subtitle: subtitle,
      child: Text(cell.door.name, style: TextStyle(color: textColor, fontSize: 24)),
    );
  }

  String getSubtitle(Gan test) {
    String subtitle = "";
    final gzdate = cell.desk.gzdate;
    final sk = ShengKe.byCompare(gzdate.d.zhi, test);
    subtitle += sk.wangxiang;
    final zhis = cell.p["地支"] as List<String>;
    final state = LifeState.byGan(test, zhis.first);
    subtitle += state.name;
    return subtitle;
  }

  Widget buildGanItem(String ganName) {
    final gan = Gan.byName(ganName);
    String subtitle = getSubtitle(gan);
    final fb = cell.fourBad(ganName);
    Color backColor = Colors.transparent;
    if (fb.contains('墓')) backColor = Colors.brown.shade200;
    Color textColor = Colors.black;
    if (fb.contains('刑')) textColor = Colors.green;
    return ItemRender(
      subtitle: subtitle,
      child: Container(
        decoration: BoxDecoration(
          color: backColor,
          borderRadius: BorderRadius.circular(3),
        ),
        padding: const EdgeInsets.fromLTRB(2, 4, 2, 1),
        child: Text(
          gan.name,
          style: TextStyle(color: textColor, height: 1.0),
        ),
      ),
    );
  }
}
