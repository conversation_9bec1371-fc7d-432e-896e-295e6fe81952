import 'package:adv_fortune/pages/qimen/widgets/cell_render.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wu_qimen/wu_qimen.dart';

class DeskRender extends StatelessWidget {
  final QimenDesk desk;
  const DeskRender({super.key, required this.desk});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Get.theme.scaffoldBackgroundColor,
      child: Table(
        border: TableBorder.all(),
        children: [
          TableRow(
            children: [
              Cell<PERSON><PERSON>(cell: desk.cells[3]),
              <PERSON><PERSON><PERSON>(cell: desk.cells[4]),
              <PERSON><PERSON><PERSON>(cell: desk.cells[5]),
            ],
          ),
          TableRow(
            children: [
              Cell<PERSON><PERSON>(cell: desk.cells[2]),
              <PERSON><PERSON><PERSON>(cell: desk.cells[8]),
              <PERSON><PERSON><PERSON>(cell: desk.cells[6]),
            ],
          ),
          TableR<PERSON>(
            children: [
              Cell<PERSON><PERSON>(cell: desk.cells[1]),
              <PERSON><PERSON>ender(cell: desk.cells[0]),
              CellRender(cell: desk.cells[7]),
            ],
          ),
        ],
      ),
    );
  }
}
