import 'package:flutter/material.dart';

class CellRow extends StatelessWidget {
  final Widget left;
  final Widget center;
  final Widget right;
  const CellRow({
    super.key,
    required this.left,
    required this.center,
    required this.right,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [left, right],
        ),
        center,
      ],
    );
  }
}
