import 'package:get/get.dart';
import 'package:wu_qimen/wu_qimen.dart';

import 'cell_controller.dart';

export 'package:get/get.dart';

class DeskController extends GetxController {
  Set<String> pikes = {'年', '月', '日', '時', '使', '戊', '生'};

  void init() {
    _deskMode = DeskMode.yin;
    _solar = DateTime.now();
    _turn = 0;
    update();
  }

  /// 開盤法
  DeskMode _deskMode = DeskMode.yin;
  DeskMode get deskMode => _deskMode;
  set deskMode(DeskMode value) {
    _deskMode = value;
    _turn = 0;
    update();
  }

  /// 開盤時間
  DateTime _solar = DateTime.now();
  DateTime get solar => _solar;
  set solar(DateTime value) {
    _solar = value;
    _turn = 0;
    update();
  }

  /// 轉盤，用於移星換斗
  int _turn = 0;
  int get turn => _turn;
  set turn(int value) {
    _turn = value;
    update();
  }

  late QimenDesk desk;

  @override
  void update([List<Object>? ids, bool condition = true]) {
    final native = QimenBuilder.build(deskMode: deskMode, solar: solar);
    desk = native.turn(turn);
    final gzdate = desk.gzdate;
    final cellCtrl = Get.find<CellController>();
    cellCtrl.cell = desk.cellFirst("天盤", gzdate.d.gan.name)!;
    super.update(ids, condition);
  }
}
