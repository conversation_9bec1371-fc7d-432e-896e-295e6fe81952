import 'package:get/get.dart';
import 'package:wu_qimen/wu_qimen.dart';

class PikeController extends GetxController {
  /// 奇門盤
  QimenDesk _desk = QimenDesk();
  QimenDesk get desk => _desk;
  set desk(QimenDesk value) {
    _desk = value;
    update();
  }

  /// 定矛1選項
  final pike1List = ["本人", "同輩", "長輩", "晚輩", "配偶"];

  /// 定矛1
  String _pike1 = '';
  String get pike1 => _pike1;
  set pike1(String value) {
    _pike1 = value;
    update();
  }

  /// 定矛2選項
  final pike2List = Gan.names + ["財運", "事業", "貴人", "感情", "學習", "健康"];

  /// 定矛2
  String _pike2 = '';
  String get pike2 => _pike2;
  set pike2(String value) {
    _pike2 = value;
    update();
  }

  QimenCell get pike1Cell => _getPikeCell(pike1);
  QimenCell get pike2Cell => _getPikeCell(pike2);

  QimenCell _getPikeCell(String pike) {
    if (Gan.names.contains(pike)) return desk.cellFirst('天', pike)!;
    switch (pike) {
      case "本人":
        return desk.cellFirst('天', desk.gzdate.d.gan.name)!;
      case "同輩":
        return desk.cellFirst('天', desk.gzdate.m.gan.name)!;
      case "長輩":
        return desk.cellFirst('天', desk.gzdate.y.gan.name)!;
      case "晚輩":
        return desk.cellFirst('地', desk.gzdate.h.gan.name)!;
      case "配偶":
        final gan = Gan.byIndex(desk.gzdate.d.gan.index + 5);
        return desk.cellFirst('地', gan.name)!;
      case "財運":
        return desk.cellFirst('門', "生")!;
      case "事業":
        return desk.cellFirst('門', "開")!;
      case "貴人":
        return desk.cellFirst('門', "休")!;
      case "感情":
        return desk.cellFirst('神', '六合')!;
      case "學習":
        return desk.cellFirst('星', '輔')!;
      case "健康":
        return desk.cellFirst('星', '芮')!;
      default:
        return desk.cells[2];
    }
  }
}
