import 'package:flutter/material.dart';
import 'package:wu_qimen/wu_qimen.dart';

import '../../widgets/panel_box.dart';

class DateCard extends StatelessWidget {
  final DateTime solar;
  final Color? backColor;
  final Color? textColor;
  final EdgeInsets? margin;
  final BorderRadius? borderRadius;
  const DateCard({
    super.key,
    required this.solar,
    this.backColor,
    this.textColor,
    this.margin,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    final dFormat = DateFormat("yyyy年MM月dd日");
    final lunar = Lunar.bySolar(solar);
    final tibetan = Tibetan.bySolar(solar);
    final mname = tibetan.monthName();
    final dname = tibetan.dayName();
    final title = mname != null ? "$mname\n$dname" : dname;

    final colorScheme = Theme.of(context).colorScheme;
    return SizedBox(
      width: double.infinity,
      child: PanelBox(
        margin: margin,
        padding: const EdgeInsets.all(4.0),
        backColor: backColor ?? colorScheme.primaryContainer,
        borderRadius: borderRadius,
        child: Row(
          children: [
            Flexible(
              flex: 1,
              child: DefaultTextStyle(
                style: TextStyle(color: textColor ?? colorScheme.onPrimaryContainer),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    buildRow("陽曆:", dFormat.format(solar)),
                    buildRow("陰曆:", lunar.toString()),
                    buildGzDate(solar),
                  ],
                ),
              ),
            ),
            const VerticalDivider(),
            Flexible(
              flex: 1,
              child: Column(crossAxisAlignment: CrossAxisAlignment.center, children: [
                buildRow("藏曆:", tibetan.toStringCn()),
                Text(title ?? ""),
                tibetan.multiple() == null ? const SizedBox() : Text(tibetan.multiple() ?? "")
              ]),
            )
          ],
        ),
      ),
    );
  }

  Widget buildRow(String header, String detail) {
    return Builder(builder: (context) {
      final colorScheme = Theme.of(context).colorScheme;
      var textStyle = TextTheme.of(context).labelMedium ?? TextStyle(fontSize: 12);
      textStyle = textStyle.copyWith(color: colorScheme.onSurface);
      return RichText(
        text: TextSpan(
          style: textStyle,
          children: [
            TextSpan(text: header, style: textStyle.copyWith(fontWeight: FontWeight.bold)),
            TextSpan(text: detail),
          ],
        ),
      );
    });
  }

  Widget buildGzDate(DateTime solar) {
    final gzdate = GzDate.bySolar(solar);
    return Builder(builder: (context) {
      final smallTitle = TextTheme.of(context).titleSmall;
      return DefaultTextStyle(
        style: TextTheme.of(context).titleLarge ?? TextStyle(fontSize: 16),
        child: Table(children: [
          TableRow(children: [
            Text("年", textAlign: TextAlign.center, style: smallTitle),
            Text("月", textAlign: TextAlign.center, style: smallTitle),
            Text("日", textAlign: TextAlign.center, style: smallTitle),
            Text("時", textAlign: TextAlign.center, style: smallTitle),
          ]),
          TableRow(children: [
            Text(gzdate.y.gan.name, textAlign: TextAlign.center),
            Text(gzdate.m.gan.name, textAlign: TextAlign.center),
            Text(gzdate.d.gan.name, textAlign: TextAlign.center),
            Text(gzdate.h.gan.name, textAlign: TextAlign.center),
          ]),
          TableRow(children: [
            Text(gzdate.y.zhi.name, textAlign: TextAlign.center),
            Text(gzdate.m.zhi.name, textAlign: TextAlign.center),
            Text(gzdate.d.zhi.name, textAlign: TextAlign.center),
            Text(gzdate.h.zhi.name, textAlign: TextAlign.center),
          ]),
        ]),
      );
    });
  }
}
