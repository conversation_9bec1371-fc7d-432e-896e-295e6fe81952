import 'package:flutter/material.dart';
import 'package:wu_core/widgets/table_builder.dart';
import 'package:wu_qimen/wu_qimen.dart';

final weekNames = ["日", "一", "二", "三", "四", "五", "六"];

class DatePicker extends StatelessWidget {
  final DateTime? selectedDate;
  const DatePicker({super.key, this.selectedDate});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        buildMonthlyCalendar(),
        buildZhiHours(),
      ],
    );
  }

  Widget buildMonthlyCalendar() {
    var selectedDate = this.selectedDate ?? DateTime.now();
    final now = DateTime.now();
    var startDate = DateTime.now();
    startDate = startDate.add(Duration(days: -(startDate.weekday % 7)));
    return Builder(builder: (context) {
      final colorScheme = Theme.of(context).colorScheme;
      return TableBuilder(
        columnCount: 7,
        children: [
          ...List.generate(
            weekNames.length,
            (index) => Text(
              weekNames[index],
              textAlign: TextAlign.center,
            ),
          ),
          ...List.generate(42, (index) {
            final date = startDate.add(Duration(days: index));
            var textColor = colorScheme.onSurface;
            if (date.weekday == 7) textColor = Colors.red;
            if (date.weekday == 6) textColor = Colors.green;
            var decoration = BoxDecoration(
              borderRadius: BorderRadius.circular(4.0),
              border: Border.all(
                width: 1,
                color: date.isSameDate(selectedDate) ? Colors.purple : Colors.transparent,
              ),
            );
            if (date.isSameDate(now)) {
              textColor = colorScheme.onPrimary;
              decoration = decoration.copyWith(
                color: colorScheme.primary,
              );
            } else {
              decoration = decoration.copyWith(
                color: Colors.transparent,
              );
            }
            return Container(
              decoration: decoration,
              child: Text(
                date.day.toString(),
                textAlign: TextAlign.center,
                style: TextStyle(color: textColor),
              ),
            );
          }),
        ],
      );
    });
  }

  Widget buildZhiHours() {
    var selectedDate = this.selectedDate ?? DateTime.now();
    final now = DateTime.now();
    return Builder(builder: (context) {
      final colorScheme = Theme.of(context).colorScheme;
      var decoration = BoxDecoration(
        borderRadius: BorderRadius.circular(4.0),
      );

      return TableBuilder(
        columnCount: 6,
        children: List.generate(12, (index) {
          final zhi = Zhi.byIndex(index);
          var textColor = colorScheme.onSurface;

          if (selectedDate.hour.isBetween(zhi.hour, zhi.hour + 1)) {
            textColor = colorScheme.onPrimary;
            decoration = decoration.copyWith(
              color: colorScheme.primary,
            );
          } else {
            decoration = decoration.copyWith(
              color: Colors.transparent,
            );
          }

          decoration = decoration.copyWith(
            border: Border.all(
              width: 1,
              color: now.hour.isBetween(zhi.hour, zhi.hour + 1) ? Colors.purple : Colors.transparent,
            ),
          );
          return Container(
            decoration: decoration,
            child: Text(
              "${zhi.name}${zhi.hourM}",
              textAlign: TextAlign.center,
              style: TextStyle(color: textColor),
            ),
          );
        }),
      );
    });
  }
}
