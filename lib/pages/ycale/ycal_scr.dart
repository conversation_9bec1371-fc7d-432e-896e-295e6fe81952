import 'package:flutter/material.dart';

import '../qimen/outdoor/outdoor.dart';
import 'date_card.dart';
import 'date_picker.dart';

class YcalScr extends StatelessWidget {
  const YcalScr({super.key});

  @override
  Widget build(BuildContext context) {
    final solar = DateTime.now();
    return Row(
      mainAxisSize: MainAxisSize.max,
      children: [
        Expanded(
          child: Column(
            spacing: 4,
            children: [
              DateCard(solar: solar),
              Outdoor(solar: solar),
              SizedBox.shrink(),
            ],
          ),
        ),
        // DateCard(solar: solar),
        SizedBox(width: 250, child: DatePicker()),
      ],
    );
  }
}
