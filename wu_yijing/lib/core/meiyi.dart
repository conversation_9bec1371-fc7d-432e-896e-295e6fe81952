import 'package:wu_fortune/wu_fortune.dart';

import 'yijing.dart';

class Meiyi {
  final Yijing yijing;
  final Gan<PERSON>hi m;

  <PERSON>yi(this.yijing, this.m) {
    _initialize();
  }

  late final int yongGua; // 用卦
  late final List<Gua64> gua64s; // 本卦、互卦、變卦
  late final List<Map<String, dynamic>> gua8s; // 本卦體、本卦用、互卦上、互卦下、變卦用

  void _initialize() {
    // 驗證只有一個動爻
    if (yijing.dongYao.length != 1) {
      throw Exception("梅花易數只能用於動爻數量為1的卦象");
    }

    // 找到動爻的位置
    final dongIndex = YAO_NAMES.indexOf(yijing.dongYao.first);

    // 判斷動爻在上卦還是下卦 = 用卦
    yongGua = dongIndex < 3 ? 0 : 1;

    // 獲取三個卦
    gua64s = [yijing.gua64s["本卦"]!, yijing.gua64s["互卦"]!, yijing.gua64s["變卦"]!];

    // 按照體用規則構建八卦數組
    gua8s = [
      {"八卦": gua64s[0].gua8s[1 - yongGua]},
      {"八卦": gua64s[0].gua8s[yongGua]},
      {"八卦": gua64s[1].gua8s[0]},
      {"八卦": gua64s[1].gua8s[1]},
      {"八卦": gua64s[2].gua8s[yongGua]},
    ];

    // 計算每個卦的屬性和關係
    _calculateGuaProperties(gua8s, m);
  }

  // 計算卦的屬性和關係
  void _calculateGuaProperties(List<Map<String, dynamic>> gua8s, GanZhi m) {
    for (var i = 0; i < gua8s.length; i++) {
      gua8s[i]["卦序"] = gua8s[i]["八卦"]?.index;
      gua8s[i]["卦名"] = gua8s[i]["八卦"]?.name;
      gua8s[i]["五行"] = gua8s[i]["八卦"]?.wuxing.name;

      if (i == 0) continue;

      final sk = ShengKe.byCompare(gua8s[i]["八卦"], gua8s[0]["八卦"]);
      gua8s[i]["生剋"] = sk.name;
      gua8s[i]["吉凶"] = sk.jixiong;
      gua8s[i]["分數"] = sk.score;

      final skm = ShengKe.byCompare(m.zhi, gua8s[0]["八卦"]);
      gua8s[i]["旺衰"] = skm.wangxiang;
    }
  }

  String toPrompt() {
    // late final int yongGua; // 用卦
    // late final List<Gua64> gua64s; // 本卦、互卦、變卦
    // late final List<Map<String, dynamic>> gua8s; // 本卦體、本卦用、互卦上、互卦下、變卦用
    var ret = '';
    const usefulGua64 = ['本卦', "互卦", "變卦"];
    ret += "## 卦象\n";
    for (var index = 0; index < gua64s.length; index++) {
      final name = usefulGua64[index];
      final gua64 = gua64s[index];
      ret += "* **$name** ${gua64.fullname}\n";
    }
    const usefulGua8 = ["本卦體", "本卦用", "互卦上", "互卦下", "變卦用"];
    for (var index = 0; index < gua8s.length; index++) {
      final name = usefulGua8[index];
      final gua8 = gua8s[index];
      ret += "* **$name** ${gua8['卦名']}。吉凶：${gua8['吉凶']}。旺衰：${gua8['旺衰']}\n";
    }
    ret += "\n";
    return ret;
  }
}
