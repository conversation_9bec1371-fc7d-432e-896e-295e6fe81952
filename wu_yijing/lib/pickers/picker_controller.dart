import 'package:get/get.dart';

import 'picker_data.dart';

export 'package:get/get.dart';
export 'package:wu_fortune/wu_fortune.dart';
export 'picker_data.dart';
export 'result_tile.dart';
export '../assets/signs.dart';
export '../assets/cards.dart';

class PickerController extends GetxController {
  PickerData _pickerData = PickerData();

  PickerData get pickerData => _pickerData;

  set pickerData(PickerData value) {
    _pickerData = value;
    update();
  }

  bool get isRolled => pickerData.sign6.length == 6;
}
