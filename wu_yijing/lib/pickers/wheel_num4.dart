import 'package:flutter/material.dart';
import 'package:wu_core/wu_public.dart';

import '../core/yijing.dart';
import 'picker_controller.dart';

/// 四滾輪起卦
class WheelNum4 extends StatefulWidget {
  const WheelNum4({super.key});

  @override
  State<WheelNum4> createState() => _WheelNum4State();
}

class _WheelNum4State extends State<WheelNum4> {
  late final List<List<String>> _cardGroup;
  late final List<List<WheelItem>> wheelsItems;
  List<String> result = [];

  void updatePickerData() {
    final controller = Get.find<PickerController>();
    final yijing = Yijing.byGuaName(result[0] + result[1], result[2] + result[3]);
    controller.pickerData = PickerData(
      name: '四數輪',
      note: '${result.join()} 得卦 ${yijing.name}',
      sign6: yijing.sign6,
    );
  }

  @override
  void initState() {
    super.initState();
    _cardGroup = List.generate(4, (index) => Gua8.names);
    wheelsItems = _cardGroup.map((group) => group.map((e) => WheelItem(Text(e), e)).toList()).toList();
    result = List.generate(_cardGroup.length, (index) => _cardGroup[index].first);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        body: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(_cardGroup.length, (index) {
              return WheelPicker(
                items: wheelsItems[index],
                width: 60,
                onSelectedItemChanged: (value) => wheelChanged(index, value),
              );
            }),
          ),
        ),
      ),
    );
  }

  void wheelChanged(int index, String value) {
    result[index] = value;
    updatePickerData();
  }
}
