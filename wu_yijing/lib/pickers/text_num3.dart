import 'dart:math';

import 'package:flutter/material.dart';
import 'package:wu_fortune/wu_calendar.dart';

import '../core/yijing.dart';
import 'picker_controller.dart';

/// 輸入三數起卦
class TextNum3 extends StatefulWidget {
  const TextNum3({super.key});

  @override
  State<TextNum3> createState() => _TextNum3State();
}

class _TextNum3State extends State<TextNum3> {
  final List<int> result = List.filled(3, 0);
  final List<TextEditingController> numCtrls = List.generate(3, (index) => TextEditingController());

  void updatePickerData() {
    final controller = Get.find<PickerController>();
    final yijing = Yijing.byNum3(result[0], result[1], result[2]);
    controller.pickerData = PickerData(name: '四卡牌', note: '${result.join()} 得卦 ${yijing.name}', sign6: yijing.sign6);
  }

  @override
  Widget build(BuildContext context) {
    // 輸入三數、陽曆取卦、陰曆取卦
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Align(
          alignment: Alignment.topCenter,
          child: SizedBox(
            width: 250,
            child: Column(
              spacing: 8,
              children: [
                _buildNumTextField(0),
                _buildNumTextField(1),
                _buildNumTextField(2),
                _buildSolarPickerButton(),
                _buildLunarPickerButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNumTextField(int index) {
    return TextFormField(
      autofocus: true,
      controller: numCtrls[index],
      decoration: InputDecoration(
        border: const OutlineInputBorder(),
        suffixIcon: IconButton(
          icon: const Icon(Icons.casino),
          onPressed: () {
            final rnd = Random();
            final randomNum = rnd.nextInt(998) + 1; // 1-9
            numCtrls[index].text = randomNum.toString();
          },
        ),
      ),
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'Required';
        }
        if (int.tryParse(value) == null) {
          return 'Must be a number';
        }
        final number = int.parse(value);
        if (number < 1 || number > 999) {
          return 'Must be between 1 and 999';
        }
        return null;
      },
      keyboardType: TextInputType.number,
      onChanged: (value) {
        if (value.isNotEmpty) {
          result[index] = int.parse(value);
          updatePickerData();
        }
      },
    );
  }

  Widget _buildSolarPickerButton() {
    final solar = DateTime.now();
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Theme.of(context).colorScheme.onPrimary,
          shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(8))),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        ),
        child: const Text('陽曆取卦'),
        onPressed: () {
          numCtrls[0].text = ((solar.month + solar.day) % 8 + 1).toString();
          numCtrls[1].text = ((solar.month + solar.day + solar.hour) % 8 + 1).toString();
          numCtrls[2].text = (solar.hour % 6 + 1).toString();
        },
      ),
    );
  }

  Widget _buildLunarPickerButton() {
    final lunar = Lunar.bySolar(DateTime.now());
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).colorScheme.primary,
          foregroundColor: Theme.of(context).colorScheme.onPrimary,
          shape: const RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(8))),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        ),
        child: const Text('陰曆取卦'),
        onPressed: () {
          numCtrls[0].text = ((lunar.m + lunar.d) % 8 + 1).toString();
          numCtrls[1].text = ((lunar.m + lunar.d + lunar.h) % 8 + 1).toString();
          numCtrls[2].text = (lunar.h % 6 + 1).toString();
        },
      ),
    );
  }
}
