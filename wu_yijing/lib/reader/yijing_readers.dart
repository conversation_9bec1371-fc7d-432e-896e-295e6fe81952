import 'package:wu_fortune/wu_calendar.dart';
import 'package:wu_llm/core/reader_base.dart';

import '../core/liuyao.dart';
import '../core/meiyi.dart';
import '../core/yijing.dart';

part 'reader_gaodao.dart';
part 'reader_tiyong.dart';
part 'reader_liuyao.dart';
part 'reader_zhouyi.dart';
part 'reader_zy_simple.dart';

enum YijingReaderMode {
  zhouyi("易經式"), // 易經式解讀
  zySimple("周易精簡"), // 易經式解讀
  gaodao("高島流"), // 高島流解讀
  tiyong("體用式"), // 體用式解讀
  liuyao("六爻式") // 六爻式解讀
  ;

  final String name;
  const YijingReaderMode(this.name);

  static YijingReaderMode byName(String? name) {
    return values.firstWhere((e) => e.name == name, orElse: () => YijingReaderMode.zhouyi);
  }
}

ReaderBase getYijingReader(YijingReaderMode mode, GzDict gzDict, Yijing yijing, String question) {
  return switch (mode) {
    YijingReaderMode.zhouyi => ReaderZhouyi(gzDict, yijing, question),
    YijingReaderMode.zySimple => ReaderZySimple(gzDict, yijing, question),
    YijingReaderMode.gaodao => ReaderGaodao(gzDict, yijing, question),
    YijingReaderMode.tiyong => ReaderTiyong(gzDict, yijing, question),
    YijingReaderMode.liuyao => ReaderLiuyao(gzDict, yijing, question),
  };
}
