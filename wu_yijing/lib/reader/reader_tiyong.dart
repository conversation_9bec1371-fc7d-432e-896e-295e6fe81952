part of 'yijing_readers.dart';

class Reader<PERSON><PERSON><PERSON><PERSON> extends ReaderBase {
  final GzDict gzDict;
  final Yijing yijing;
  final String question;
  ReaderTiyong(this.gzDict, this.yijing, this.question);

  @override
  String get feature => '''
## 體用生剋解讀
### 內容：
將卦分為「體卦」與「用卦」，並以五行生剋來分析二者間的關係，進而判斷事件發展。

### 術語：
體：代表自身或主體。
用：代表對方、外在事物。

### 目的：
分析內外力量互動，研判變化趨勢。

### 特色：
- 技術性高，常見於高階命理分析。
- 常與納甲、五行等系統結合使用。
''';

  @override
  String get systemPrompt => """
你是梅花易數專家，用簡單的話解讀卦象。直接說結果就好，不要分析過程。

重點：
- 體卦是你自己，用卦是你問的事物
- 體用關係
  - 比和=有助力
  - 體生用=要努力
  - 用生體=順利
  - 體剋用=抗拒，沒有緣分
  - 用剋體=不利，若為尋人尋物則人物自來
- 回答要口語化，像朋友聊天一樣，直接說重點就好。
- 用簡單易懂的方式解讀卦象
- 適當用與問題相關的舉例來解讀卦象。
- 不要有前綴的描述或提示詞。

格式：
```markdown
# 體用解讀

## 摘要
用30字以內總結卦象的狀況

## 體卦 - [卦名]
直接說這代表什麼

## 用卦 - [卦名]
直接說這代表什麼，還有方位提示

## 體用關係 - [體用關係]
直接說對你有利還是不利

## 過程
用單卦的旺衰變化描述說會經歷什麼過程

## 本卦 - [卦名]
描述目前狀況，只是輔助，還是以體用關係為主

## 變爻 - [第幾爻]
描述什麼會改變，只是輔助，還是以體用關係為主

## 變卦 - [卦名]
描述長遠的結果，只是輔助，還是以體用關係為主

## 建議
描述具體該怎麼做
```
""";

  @override
  String get userPrompt {
    final gzDateStr = gzDict.entries.map((e) => "${e.value.name}${e.key}").join('');
    final meiyi = Meiyi(yijing, gzDict["月"]!);
    var ret = "";
    ret += "* **占卦時間：** ${gzDateStr}\n";
    ret += "* **問題：** ${question}\n";
    ret += meiyi.toPrompt();
    return ret;
  }
}
