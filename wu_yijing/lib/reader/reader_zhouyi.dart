part of 'yijing_readers.dart';

class Reader<PERSON><PERSON>yi extends ReaderBase {
  final GzDict gzDict;
  final Yijing yijing;
  final String question;
  ReaderZhouyi(this.gzDict, this.yijing, this.question);

  @override
  String get feature => '''
## 義理解讀（象數哲理）
### 內容：
強調《易經》的哲學義理與宇宙觀，如「天行健，君子以自強不息」、「陰陽互動」、「中庸之道」等。

### 目的：
用於修身養性、理解天地人三才的運作。

### 代表人物：
程頤、朱熹（理學派）。

### 特色：
- 解卦如詩，重在精神層面。
- 非預測工具，而是修養的經典。
''';

  @override
  String get systemPrompt => """
你是社會經驗豐富的易經老師，善於用白話文解讀卦象
- 用簡單易懂的方式解讀卦象
- 適當的用與問題相關的舉例來解讀卦象。
- 回答要口語化，像朋友聊天一樣。
- 請直接描述結果，不要分析過程。
- 不要有前綴的描述或提示詞。

所得為動卦時：
- 本卦：現在的狀況
- 變卦：最終結果
- 變爻：關鍵變化
- 互卦：內在因素
- 綜卦：另一個角度
- 錯卦：相反的可能

格式：
```markdown
# 周易解讀

## 摘要
用30字以內總結卦象的狀況

## 本卦 - [卦名]
現在的狀況

## 變卦 - [卦名]
最終結果

## 變爻 - [第幾爻]
關鍵變化

## 互卦 - [卦名]
內在因素

## 綜卦 - [卦名]
從完全相反的角度看事情，揭示潛在的對立面或互補關係，思考如果所有條件都翻轉會是什麼結果。

## 錯卦 - [卦名]
從另一個立場或顛倒的視角看問題，揭示事物內部不同面向的關係，思考如果換個角度或換個人來看會發現什麼。

## 總結
總結前面各卦象的內容

## 實用建議
描述該怎麼做，並提供適當的建議
```

所得為靜卦時：
- 專注於本卦卦辭：靜卦代表所測之事處於一個相對穩定、沒有明顯變化的狀態。
- 理解卦象的象徵意義：除了卦辭，也要理解該卦本身所代表的象徵意義。例如乾為天、坤為地、坎為險、離為明等等。
- 靜卦通常暗示著「不動則無咎」或「靜觀其變」。

靜卦格式：
```markdown
# 周易解讀

## 摘要
用30字以內總結卦象的狀況

## 靜卦 [卦名]
用白話詳細說明卦辭代表的意義

## 實用建議
描述該怎麼做，並提供適當的建議
```

全變格式：
```markdown
# 周易解讀

## 摘要
用30字以內總結卦象的狀況

## 全變以變卦解讀 [卦名]
用變卦卦辭進行解讀

## 實用建議
描述該怎麼做，並提供適當的建議
```
""";

  @override
  String get userPrompt {
    final gzDateStr = gzDict.entries.map((e) => "${e.value.name}${e.key}").join('');
    String userPrompt = """
* **占卦時間：** ${gzDateStr}
* **問題：** ${question}
* **本卦：** ${yijing.gua64s["本卦"]?.fullname}
* **變卦：** ${yijing.gua64s["變卦"]?.fullname}
* **互卦：** ${yijing.gua64s["互卦"]?.fullname}
* **錯卦：** ${yijing.gua64s["錯卦"]?.fullname}
* **綜卦：** ${yijing.gua64s["綜卦"]?.fullname}
""";
// 無變爻： 僅以本卦卦辭解讀 。
// 一爻變： 以該變爻的爻辭為主解讀。
// 兩爻變：
//  也有說法是兩爻辭並重，或以之卦卦辭輔助 。
// 三爻變： 以中間的變爻爻辭為主
// 四爻變： 以本卦中唯一不變的兩爻中，靠近上方的爻辭為主。
// 五爻變： 以本卦中唯一不變的爻辭為主 。
// 六爻皆變： 以之卦卦辭解讀，因為本卦的意義已耗盡 。

    switch (yijing.dongYao.length) {
      case 0:
        userPrompt = """
* **占卦時間：** ${gzDateStr}
* **問題：** ${question}
* **本卦：(靜卦)** ${Gua64.bySign6(yijing.orginal).fullname}
""";
        break;
      case 1:
        userPrompt += "* **變爻：** ${yijing.dongYao.last}\n";
        break;
      case 2:
        userPrompt += "* **變爻：** ${yijing.dongYao.join('、')}\n";
        break;
      case 3:
        userPrompt += "* **變爻：** ${yijing.dongYao[1]}\n";
        break;
      case 4:
        userPrompt += "* **變爻：** ${yijing.jingYao.first}\n";
        break;
      case 5:
        userPrompt += "* **變爻：** ${yijing.jingYao.first}\n";
        break;
      case 6:
        userPrompt = """
* **占卦時間：** ${gzDateStr}
* **問題：** ${question}
* **六爻全變：** ${yijing.gua64s["變卦"]?.fullname}
* 以全變格式解讀
""";
        break;
    }
    // print("${yijing.dongYao.length}");
    // print(userPrompt);
    return userPrompt;
  }
}
