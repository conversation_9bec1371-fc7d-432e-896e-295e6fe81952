import 'package:flutter/material.dart';

class Cards {
  /// 卡牌背面
  static Widget back({double? width}) {
    return Image.asset('packages/wu_yijing/assets/cards/牌背.png', width: width);
  }

  /// 八卦
  static Widget bagua(String id, {double? width}) {
    return Image.asset('packages/wu_yijing/assets/cards/八卦/$id.png', width: width);
  }

  /// 銅錢
  static Widget coins(String id, {double? width}) {
    return Image.asset('packages/wu_yijing/assets/cards/銅錢/$id.png', width: width);
  }

  /// 骰子
  static Widget dice(String id, {double? width}) {
    return Image.asset('packages/wu_yijing/assets/cards/骰子/$id.png', width: width);
  }

  /// 梅花易數
  static Widget meiyi(String id, {double? width}) {
    return Image.asset('packages/wu_yijing/assets/cards/梅易/$id.png', width: width);
  }

  /// 六爻
  static Widget liuyao(String id, {double? width}) {
    return Image.asset('packages/wu_yijing/assets/cards/六爻/$id.png', width: width);
  }
}
