import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class Signs {
  static Widget yinYang(String sign, {double? width}) {
    final path = ['-', 'o'].contains(sign) ? "陽" : "陰";
    final color = ['x', 'o'].contains(sign) ? Colors.red : Colors.black;
    return SvgPicture.asset(
      'packages/wu_fortune/assets/signs/${path}.svg',
      width: width,
      colorFilter: ColorFilter.mode(color, BlendMode.srcIn),
    );
  }

  static Widget sign(String sign, {double? width}) {
    return SvgPicture.asset('packages/wu_fortune/assets/signs/sign${sign.toLowerCase()}.svg', width: width);
  }
}
