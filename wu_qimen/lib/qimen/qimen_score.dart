part of '../wu_qimen.dart';

class QimenScore {
  /// 磁場開運法
  /// 吉門+吉星神格為大吉、吉門+凶星神格為小吉
  /// 凶門+吉星神格為大吉、凶門+凶星神格為大凶
  /// 命盤30%，年盤10%，月盤10%，日盤15%，時盤35%
  static final Map<String, int> scores = {
    "符": 20, "陰": 20, "六": 20, "天": 20, // 神
    "輔": 20, "心": 20, "任": 20, // 星
    "開": 40, "休": 40, "生": 40, "景": 20, // 門
    "乙": 10, "丙": 10, "丁": 10, "戊": 10, // 干
  };

  QimenScore();

  /// 以顏色顯示分數
  static Text text(int value) {
    final ret = value > 0
        ? Text(value.toString(), style: const TextStyle(color: Colors.red))
        : Text((-value).toString(), style: const TextStyle(color: Colors.green));
    return ret;
  }

  /// 宮分數計算
  static int cellScore(QimenCell cell) {
    int ret = 0;
    ret += scores[cell.p["神"]?.name] ?? 0; // Add null-aware access
    ret += scores[cell.p["星"]?.name] ?? 0; // Add null-aware access
    ret += scores[cell.p["門"]?.name] ?? 0; // Add null-aware access
    ret += scores[cell.p["地盤"]?.name] ?? 0; // Add null-aware access
    if (cell.fourBad(cell.door.name) == "迫") ret += -30;
    if (cell.p["空"] ?? false) ret += -20;
    // 格局
    const Set<String> goodPatternTitles = {};
    const Set<String> badPatternTitles = {};

    for (var patternItem in cell.geju) {
      // Changed cell.geju to cell.patterns
      if (badPatternTitles.contains(patternItem.title)) {
        ret += -20;
      }
      if (goodPatternTitles.contains(patternItem.title)) {
        ret += 30;
      }
    }
    ret += cell.desk.fuyin().length * -5;
    ret += cell.desk.fanyin().length * -5;
    return ret;
  }

  // 盤計算分數
  static void calcDesk(QimenDesk desk) {
    for (var cell in desk.cells.take(8)) {
      cell.p["score"] = cellScore(cell); // Store score in the cell's dynamic properties map
    }
  }
}
