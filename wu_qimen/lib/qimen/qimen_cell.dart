part of '../wu_qimen.dart';

class QimenCell {
  final QimenDesk desk;
  final Map<String, dynamic> p = {};

  QimenCell(
    this.desk,
    int serial,
    int luoshu,
    String nature,
    String nurture,
    String orignalStar,
    String orignalDoor,
    String numbers,
    String zhis,
  ) {
    p['順序'] = serial;
    p['洛書'] = luoshu;
    p['先天'] = nature;
    p['後天'] = nurture;
    p['原星'] = orignalStar;
    p['原門'] = orignalDoor;
    p['數字'] = numbers;
    p['地支'] = zhis.split("");
    // 神、星、門、天盤、地盤、引干、寄天、寄地
  }

  int get serial => p['順序'] as int;
  int get luoshu => p['洛書'] as int;
  List<String> get zhis => p['地支'] as List<String>;
  QimenStar get star => QimenStar.byName(p['星']);
  QimenDoor get door => QimenDoor.byName(p['門']);
  Gan get tian => Gan.byName(p['天盤']);
  Gan get di => Gan.byName(p['地盤']);
  Gan get yingan => Gan.byName(p['引干']);
  Gan? get jiTian => p['寄天'] == null ? null : Gan.byName(p['寄天']);
  Gan? get jiDi => p['寄地'] == null ? null : Gan.byName(p['寄地']);

  List<GejuCell> get geju => GejuCell.getList(this);

  bool isTian(String ganName) {
    if (ganName == '甲') return p['神'] == '符';
    return p['天盤'] == ganName;
  }

  bool test(String propName, dynamic values) {
    return values.contains(p[propName]);
  }

  List<String> get keyProps => [p['神'], p['星'], p['門'], p['天盤']];

  String get describe {
    String? itemString(String title, String? value, String? attr) {
      if (value == null) return null;
      String ret = "$title: $value";
      if (attr != null) ret += "::$attr";
      return ret;
    }

    String doorAttr() {
      String ret = '';
      final sk = ShengKe.byCompare(desk.gzdate.d.zhi, door);
      final fb = fourBad(p['門']);
      ret += sk.wangxiang;
      if (fb.isNotEmpty) ret += "($fb)";
      return ret;
    }

    String ganAttr(String? ganName) {
      if (ganName == null) return '';
      String ret = '';
      final gan = Gan.byName(ganName);
      final sk = ShengKe.byCompare(desk.gzdate.d.zhi, gan);
      final state = LifeState.byGan(gan, zhis.first);
      ret += "${sk.wangxiang},${state.longName}";
      var fb = fourBad(ganName);
      fb = fb.replaceAll("迫", "門迫");
      fb = fb.replaceAll("制", "門制");
      fb = fb.replaceAll("墓", "入墓");
      fb = fb.replaceAll("刑", "擊刑");
      if (fb.isNotEmpty) ret += ",$fb";
      return ret;
    }

    String ret = "";
    final items = [
      itemString("宮", p['後天'], null),
      itemString("神", p['神'], null),
      itemString("星", p['星'], starAbility()),
      itemString("門", p['門'], doorAttr()),
      itemString("天盤", p['天盤'], ganAttr(p['天盤'])),
      itemString("地盤", p['地盤'], ganAttr(p['地盤'])),
      itemString("寄天", p['寄天'], ganAttr(p['寄天'])),
      itemString("寄地", p['寄地'], ganAttr(p['寄地'])),
      itemString("引干", p['引干'], ganAttr(p['引干'])),
    ];
    ret += "## 元素\n${items.where((e) => e != null).join(",")}";
    if (p["空"] ?? false) ret += ",空亡\n";
    if (p["馬"] ?? false) ret += ",馬星\n";
    ret += "\n\n";

    final gejuList = GejuCell.getList(this);
    if (gejuList.isNotEmpty) {
      ret += "## 格局\n";
      for (var p in gejuList) {
        ret += "- ${p.title}\n";
      }
    }
    return ret;
  }

  /// 定矛
  List<String> getPikes() {
    final ret = <String>[];
    final gzdate = desk.gzdate;
    if (p['寄天'] == gzdate.y.gan.name) ret.add('年');
    if (p['天盤'] == gzdate.y.gan.name) ret.add('年');
    if (p['寄天'] == gzdate.m.gan.name) ret.add('月');
    if (p['天盤'] == gzdate.m.gan.name) ret.add('月');
    if (p['寄天'] == gzdate.d.gan.name) ret.add('日');
    if (p['天盤'] == gzdate.d.gan.name) ret.add('日');
    if (p['寄天'] == gzdate.h.gan.name) ret.add('時');
    if (p['天盤'] == gzdate.h.gan.name) ret.add('時');
    if (p['神'] == desk.p['符']) ret.add('符');
    if (p['門'] == desk.p['值使']) ret.add('使');

    // Shishen("才", "偏財", "我尅同", {
    // Shishen("財", "正財", "我尅異", {

    bool testTian(String name) {
      if (name == '甲') return p['神'] == '符';
      return p['天盤'] == name || p['寄天'] == name;
    }

    final me = gzdate.d.gan;
    final cai = Shishen.getCai("偏財", me);
    if (testTian(cai.name)) ret.add('才');
    final cai2 = Shishen.getCai("正財", me);
    if (testTian(cai2.name)) ret.add('財');
    // print("$cai $cai2");

    return ret;
  }

  /// 奇門四害
  /// 空亡：宮位的能量已經被抽走轉移
  /// 門迫：內在的不好，自身的能量不足，內部有問題
  /// 擊刑：外在的不好
  /// 入墓：封印，力量得不到發揮，心有餘力不足
  /// 伏吟：潛伏不動
  /// 反吟：反覆
  String fourBad(String symbol) {
    var ret = "";
    final test = p['後天'] + symbol;

    // 入墓 艮：丁己庚，巽：辛壬，坤：符癸，乾：乙丙戊
    if (["艮丁", "艮己", "艮庚", "巽辛", "巽壬", "坤癸", "乾乙", "乾丙", "乾戊"].contains(test)) {
      ret += "墓";
    }

    // 六儀擊刑：主謀事不成，諸事不順
    // 擊刑 震：戊，巽：癸，離：辛，巽：壬癸
    if (["震戊", "坤己", "艮庚", "離辛", "巽壬", "巽癸"].contains(test)) {
      ret += "刑";
    }

    // 門迫，吉門不吉凶門更凶
    // 門迫 艮傷，震開，巽開，離休，坤傷，兌景，乾景，兌生
    if ([
      "巽開", "巽驚", "震開", "震驚", "坤傷", "坤杜", //
      "離休", "艮傷", "艮杜", "坎生", "坎死", "兌景", //
      "乾景",
    ].contains(test)) {
      ret += "迫";
    }

    // 和~~八門生宮位五行，屬吉。能提升奇門功效。
    // 義~~宮生八門五行，屬吉。能增強遁甲效能。
    // 制~~八門剋宮位五行，中平。門不受宮之助力。
    // 害~~宮剋八門五行，屬凶。吉事不成，凶事尤甚 （迫）
    // if (p["門"] != null) {
    //   final menIdx = Qimen.doors.indexOf(p["門"]);
    //   final menWx = Wuxing.byName(Qimen.wuxing[menIdx]);
    //   final gongWx = Wuxing.byName(Qimen.wuxing[p["順序"] - 1]);
    //   final sk = Shengke.byCompare(menWx, gongWx);
    //   if (sk.name == "我生") ret += "和";
    //   if (sk.name == "生我") ret += "義";
    // }

    // // 門制/宮迫，吉門不吉，凶門受壓制
    if (["坤休", "兌杜", "兌傷", "乾杜", "乾傷", "坎景", "艮休", "震生", "震死", "巽生", "巽死", "離驚", "離開"].contains(test)) {
      ret += "制";
    }
    return ret;
  }

  // 神無旺衰。星門以宮看旺衰，星旺則正面，衰則反面。

  /// 九星旺衰口诀
  /// 九星對宮：的旺衰為當事人的狀態/能力（地利）。九星對月令：的狀態則為時間對當事人的需求（天時）
  /// 我生为旺，生我为废，克我则囚，我克则休，同我者相。
  String starAbility() {
    const dict = {"比合": "相", "我生": "旺", "我尅": "休", "尅我": "囚", "生我": "廢"};
    final sk = ShengKe.byCompare(star, Gua8.byName(p["後天"]));
    return dict[sk.name] ?? "";
  }
}
