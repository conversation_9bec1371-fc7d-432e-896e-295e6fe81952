part of '../wu_qimen.dart';

class QimenStar extends Noun implements IWuxing {
  static const dicts = [
    ("篷", "水"), ("任", "土"), ("沖", "木"), ("輔", "木"), //
    ("英", "火"), ("芮", "土"), ("柱", "金"), ("心", "金"), //
  ];

  static List<String> _names = [];
  static List<String> get names {
    if (_names.isEmpty) _names = dicts.map((e) => e.$1).toList();
    return _names;
  }

  String get fullname => "天$name";
  @override
  Wuxing get wuxing => Wuxing.byName(dicts[index].$2);

  QimenStar() : super(names);
  factory QimenStar.byIndex(int index) => QimenStar()..index = index;
  factory QimenStar.byName(String name) => QimenStar()..name = name;
}
