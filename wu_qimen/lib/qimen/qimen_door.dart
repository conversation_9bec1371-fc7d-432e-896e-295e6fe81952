part of '../wu_qimen.dart';

class QimenDoor extends Noun implements IWuxing {
  static final dicts = [
    ("休", "水"), ("生", "土"), ("傷", "木"), ("杜", "木"), //
    ("景", "火"), ("死", "土"), ("驚", "金"), ("開", "金"), //
  ];

  static List<String> _names = [];
  static List<String> get names {
    if (_names.isEmpty) _names = dicts.map((e) => e.$1).toList();
    return _names;
  }

  String get fullname => "$name門";
  @override
  Wuxing get wuxing => Wuxing.byName(dicts[index].$2);

  QimenDoor() : super(names);
  factory QimenDoor.byIndex(int index) => QimenDoor()..index = index;
  factory QimenDoor.byName(String name) => QimenDoor()..name = name;
}
