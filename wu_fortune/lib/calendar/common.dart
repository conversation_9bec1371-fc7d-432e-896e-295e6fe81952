import '../wu_calendar.dart';

List<String> JIEQI_NAMES = [
  "小寒", "大寒", "立春", "雨水", "驚蟄", "春分", "清明", "穀雨", "立夏", "小滿", "芒種", "夏至", //
  "小暑", "大暑", "立秋", "處暑", "白露", "秋分", "寒露", "霜降", "立冬", "小雪", "大雪", "冬至", //
];

Map<String, String> LUNAR_MONTHS = {
  "雨水": "正", "春分": "二", "穀雨": "三", "小滿": "四", "夏至": "五", "大暑": "六", //
  "處暑": "七", "秋分": "八", "霜降": "九", "小雪": "十", "冬至": "冬", "大寒": "臘", //
};

List<String> LUNAR_DAYS = [
  "初一", "初二", "初三", "初四", "初五", "初六", "初七", "初八", "初九", "初十", //
  "十一", "十二", "十三", "十四", "十五", "十六", "十七", "十八", "十九", "二十", //
  "廿一", "廿二", "廿三", "廿四", "廿五", "廿六", "廿七", "廿八", "廿九", "三十", //
];

List<String> GANS = ["甲", "乙", "丙", "丁", "戊", "己", "庚", "辛", "壬", "癸"];
List<String> ZHIS = ["子", "丑", "寅", "卯", "辰", "巳", "午", "未", "申", "酉", "戌", "亥"];
List<String> ZODIAC = ["鼠", "牛", "虎", "兔", "龍", "蛇", "馬", "羊", "猴", "雞", "狗", "豬"];

const sunRevolve = 365.242189; // 太陽回歸年
const moonRevolve = 29.5306; // 月球公轉日數

final baseYearDate = DateTime(1913, 1, 6, 5, 58); // 小寒基準日
final baseMonthDate = DateTime(2008, 2, 7, 11, 40); // 春節基準日 2008年02月07日	11:40am	新月
const baseJiaziYear = 1984; // 甲子年基準年
const baseJiaziMonth = 2003; // 甲子月基準年
final baseJiaziDate = DateTime(1912, 2, 18); // 甲子日基準日
final baseJiaziTime = DateTime(1912, 2, 17, 23, 0); // 甲子時基準日

const zhWeek = ["日", "一", "二", "三", "四", "五", "六"];
const zhMonthFull = ["一", "二", "三", "四", "五", "六", "七", "八", "九", "十", "十一", "十二"];

(String? name, String? mean) specialTime(GzDate gzdate) {
  // 五不遇時
  // 通常每天都有一個五不遇時，偶爾有二個。在奇門擇吉中一向不主張用「五不遇」，
  // 這個時辰來謀求事情，如出行、求財、找人辦事等。如果用五不遇時去辦事多數會招致不同程度的不順，
  // 輕則遇到阻隔，重者會把所辦的事情搞砸。
  //
  // 時干克日干在四柱命理學中被稱之為時遇七殺，七殺用事often凶暴無情，
  // 所以即使謀事的方位有三奇乙丙丁，或開門、休門、生門等吉格，用這個時辰仍然不吉利。
  // 可見五不遇時對事物的負面作用是相當大的，故在現實生活中若要謀求大事，應儘量避開它。
  final dhGan = gzdate.d.gan.name + gzdate.h.gan.name;
  // developer.log(dh);
  const patterns = ["甲庚", "乙辛", "丙壬", "丁癸", "戊甲", "己乙", "庚丙", "辛丁", "壬戊", "癸己"];
  if (patterns.contains(dhGan)) {
    return ("五不遇時", "用五不遇時去辦事多數會招致不同程度的不順，輕則遇到阻隔，重則會把所辦的事情搞砸。");
  }
  final dhGanZhi = gzdate.d.name + gzdate.h.name;
  // 天輔時
  if (["甲己己巳", "乙庚甲申", "丙辛甲午", "丁壬甲辰", "戊癸甲寅"].contains(dhGanZhi)) {
    return ("天輔時", "天佑開恩之時，利於請求他人原諒，開恩赦罪，能大事化小，逢凶化吉，提升奇門之吉效。");
  }
  // 時干入墓，用時時干入墓於時支，主運籌圖謀易無果
  final dGanzhi = gzdate.h.name;
  // 陰陽異長生 ["丙戌", "丁丑", "戊戌", "己丑", "壬辰"]
  if (["乙未", "丙戌", "戊戌", "辛丑", "壬辰"].contains(dGanzhi)) {
    return ("時干入墓", "主運籌圖謀易無果");
  }

  return (null, null);
}
