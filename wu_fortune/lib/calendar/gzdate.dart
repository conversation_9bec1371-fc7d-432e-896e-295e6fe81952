import 'package:wu_core/wu_extensions.dart';
import '../wu_calendar.dart';

typedef GzDict = Map<String, GanZhi>;

// 干支曆
class GzDate {
  late GanZhi y, m, d, h;

  List<String> get gans => [y.gan.name, m.gan.name, d.gan.name, h.gan.name];
  List<String> get zhis => [y.zhi.name, m.zhi.name, d.zhi.name, h.zhi.name];

  GzDate();
  factory GzDate.bySolar(DateTime solar) => GzDate()..convert(solar);
  factory GzDate.now() => GzDate.bySolar(DateTime.now());
  factory GzDate.parse(String text) {
    final regexp = RegExp(r"((?<y>.*)年)?(?<m>.*)月(?<d>.*)日((?<h>.*)時)?");
    final match = regexp.allMatches(text).first;
    return GzDate()
      ..y = GanZhi.byName(match.namedGroup("y")!)
      ..m = GanZhi.byName(match.namedGroup("m")!)
      ..d = GanZhi.byName(match.namedGroup("d")!)
      ..h = GanZhi.byName(match.namedGroup("h")!);
  }

  static GzDict toDict(String text) {
    final regexp = RegExp(r"((?<y>.*)年)?(?<m>.*)月(?<d>.*)日((?<h>.*)時)?");
    final match = regexp.allMatches(text).first;
    final result = GzDict();

    void setDict(String key, String? value) {
      if (value == null) return;
      result[key] = GanZhi.byName(value);
    }

    setDict("年", match.namedGroup("y"));
    setDict("月", match.namedGroup("m"));
    setDict("日", match.namedGroup("d"));
    setDict("時", match.namedGroup("h"));
    return result;
  }

  @override
  String toString() => "$y年$m月$d日$h時";
  String toYMD() => "$y年$m月$d日";

  /// 轉換太陽曆為干支曆
  void convert(DateTime solar) {
    // 子時修正
    var calc = GzDate.hourCalc(solar);
    var termInfo = Jieqi.bySolar(calc);
    var yIdx = calc.year;
    // 干支年從(立春#2)開始, 立春前則年份要減一
    var breakTerm = Jieqi.byYearName(calc.year, "立春");
    // print("breakTerm, ${breakTerm.index} ${breakTerm.dateTime} ${breakTerm.name}");
    if ((calc.year == breakTerm.dateTime.year) && (calc.date().compareTo(breakTerm.dateTime.date()) < 0)) {
      yIdx--;
    }
    // if ((calc.year == termInfo.dateTime.year) && (termInfo.index < 2)) yIdx--;
    // print("$solar, $calc, ${termInfo.index} ${termInfo.dateTime} ${termInfo.name} $yIdx");

    // print("$solar, $calc, ${termInfo.index} ${termInfo.dateTime} ${termInfo.name}, $yIdx");

    y = GanZhi.byIndex(yIdx - baseJiaziYear);
    var mIdx = (calc.year - baseJiaziMonth - 1) * 12 + ((termInfo.index) ~/ 2) % 60;
    m = GanZhi.byIndex(mIdx + 1);
    // 日計算
    var dIdx = calc.date().difference(baseJiaziDate).inDays;
    d = GanZhi.byIndex(dIdx);

    // 時計算
    // 一開始就對時間進行一小時修正
    // 五鼠遁：甲己=>甲子，乙庚=>丙子，丙辛=>戊子，丁壬=>庚子，戊癸=>壬子
    // 使用五鼠遁就可以避開windows的時間計算錯誤問題
    final hIdx = get5Mouse(d.gan).index;
    var hour = calc.hour % 24 ~/ 2;

    final hGan = Gan.byIndex(hIdx + hour);
    final hZhi = Zhi.byIndex(hour);
    h = GanZhi.byName(hGan.name + hZhi.name);
  }

  /// 五行在這個時間點的強弱
  List<int> intensity(IWuxing wuxing) {
    final result = [ShengKe.byCompare(m.zhi, wuxing).score - 2, ShengKe.byCompare(d.zhi, wuxing).score - 2];
    return result;
  }

  /// 時辰開始的時間
  static DateTime hourStart(DateTime solar) {
    var date = solar.add(const Duration(hours: 1));
    final startHour = (date.hour % 24) ~/ 2 * 2;
    return DateTime(date.year, date.month, date.day, startHour);
  }

  /// 時辰計算用的時間
  static DateTime hourCalc(DateTime solar) {
    return solar.add(const Duration(hours: 1));
  }

  /// 時辰的開始和結束時間
  static (DateTime time1, DateTime time2) shichenTimes(DateTime solar) {
    var workTime = solar.add(const Duration(hours: 1));
    workTime = workTime.copyWith(hour: workTime.hour ~/ 2 * 2);
    final time1 = workTime.add(const Duration(hours: -1));
    final time2 = workTime.add(const Duration(hours: 1));
    return (time1, time2);
  }
}
