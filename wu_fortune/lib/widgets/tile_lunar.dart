import 'package:flutter/material.dart';
import 'package:wu_core/wu_public.dart';

import '../wu_zeri.dart';

// 十齋日:
// 農曆每月初一、初八、十四、十五、十八、廿三、廿四、廿八、廿九、三十.
// 六齋日:
// 《大智度論》等經文提到，六齋日為初八、十四、十五、廿三、廿九、三十.
// 四齋日:
// 《阿含經》提到，四齋日為初一、初八、十五、廿三，是出家眾的布薩日，也是持八關齋戒的日子.
const _fasting = {
  "四齋": ["初一", "初八", "十五", "廿三"],
  "六齋": ["初八", "十四", "十五", "廿三", "廿九", "三十"],
  "十齋": ["初一", "初八", "十四", "十五", "十八", "廿三", "廿四", "廿八", "廿九", "三十"],
};

class TileLunar extends StatelessWidget {
  final DateTime solar;
  final Color colorLucky;
  const TileLunar({super.key, required this.solar, this.colorLucky = Colors.red});

  @override
  Widget build(BuildContext context) {
    final lunar = Lunar.bySolar(solar);
    final taoistDays = TaoistDays.getList(lunar);
    final offend = Offend(solar);
    String lunarText = lunar.toYMD();
    for (final k in _fasting.keys) {
      if (_fasting[k]!.contains(lunar.zhD)) {
        taoistDays.add("$k日");
        break;
      }
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        InfoRow(left: '農曆：', right: lunarText),
        InfoRow(left: "沖煞：", right: offend.toString()),
        ...taoistDays.map((e) => Text(e, style: TextStyle(color: colorLucky))),
        if (TaoistDays.isTiansheDay(solar)) Text("天赦日", style: TextStyle(color: colorLucky)),
      ],
    );
  }
}
