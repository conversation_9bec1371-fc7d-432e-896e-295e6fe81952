name: adv_fortune
description: "A new Flutter project."

publish_to: 'none' 

version: 1.0.0+1

environment:
  sdk: '>=3.1.3 <4.0.0'
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter

  # database
  path_provider: ^2.1.5
  hive_ce: ^2.11.3
  hive_ce_flutter: ^2.3.1

  cupertino_icons: ^1.0.8
  intl: ^0.20.2
  get: ^4.7.2
  get_storage: ^2.1.1
  flutter_markdown: ^0.7.7
  flutter_form_builder: ^10.1.0
  form_builder_validators: ^11.2.0
  grouped_list: ^6.0.0
  flex_color_scheme: ^8.2.0

  flutter_svg: ^2.2.0

  wu_core:
    path: ../adv_package/wu_core
  wu_fortune:
    path: ../adv_package/wu_fortune
  wu_qimen:
    path: ../adv_package/wu_qimen
  wu_yijing:
    path: ../adv_package/wu_yijing
  wu_tarot:
    path: ../adv_package/wu_tarot
  wu_liuren:
    path: ../adv_package/wu_liuren

dependency_overrides:
  intl: ^0.20.2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^6.0.0
  build_runner: ^2.6.0
  # dart run build_runner build
  # flutter packages pub run build_runner build

  hive_ce_generator: ^1.9.3

  flutter_launcher_icons: ^0.14.4
  # flutter pub run flutter_launcher_icons:main

  flutter_native_splash: ^2.4.6
  # flutter pub run flutter_native_splash:create

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/ai_tarot.png"
  remove_alpha_ios: true
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    background_color: "#hexcode"
    theme_color: "#hexcode"
  windows:
    generate: true
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true

flutter_native_splash:
  android: true
  ios: true
  color: "#C9BC9C"
  image: assets/ai_tarot.png
  color_dark: "#1a1a1a"
  fullscreen: true

flutter:
  uses-material-design: true
  assets:
    - assets/
    - assets/tarot/images/channels/
    - assets/tarot/images/icons/
    - assets/tarot/images/waite/
  
  
  
  

  
  

  
  

  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
