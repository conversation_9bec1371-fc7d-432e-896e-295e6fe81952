# 構思

## 網站目的

- 目標客戶開發者本人使用
- 使用Flutter Web技術，主要終端目標為手機及平板，因需要配合使用平板的陽宅平面圖軟體
- 使用Fliebase服務：
  - Firestore：用於儲存用戶資料、設定、AI API KEY等
  - Authentication：用於用戶註冊及登入
  - Functions：用於實現AI解讀功能
  - Storage：用於儲存AI解讀結果
  - Hosting：用於主機部署
  - FCM：用於推送通知功能，發送AI解讀需求，讓特殊伺服器解讀
- 提供AI解讀功能，幫助用戶理解其占卜結果

## 網站功能

> 所有頁面功能詳細內容存放於每個頁面相對資料夾的MD檔案中

- 提供各種線上命理工具，包含：
  - 西洋：占星術、塔羅牌、盧恩符文、馬雅曆、彩虹靈數
  - 中國：四柱八字、紫微斗數、奇門遁甲、六爻、梅易、易經、數字易經、靈籤
- 推命或占卜都應該以平凡用戶為主，主要直接提供解讀功能，次要才是排盤結果

## 檔案結構

- lib/pages之下為每一個主要功能頁面開設獨立的資料夾
- 主要功能頁面使用完整獨立的檔案命名
- 主要功能的次要頁面如果不被外部叫用，則使用簡要命名，例如：list_page, detail_page
- 主要功能中可以包含不被外部叫用的部件，存放於widgets資料夾中
- 其他不被外部叫用的功能都可以另開資料夾存放，例如：widgets, utils, services
- 頁面大小分S,M,L,XL，分別存為獨立檔案，大小為檔名後綴，初步開發以S為主，其他先不要做
