part of '../wu_auto_layout.dart';

/// 響應式佈局系統配置
enum SizeLevel {
  // 576,768,1024,1200
  S(breakpoint: 576), // 手機版（大螢幕手機）
  M(breakpoint: 768), // 平板版（直向）
  L(breakpoint: 1024), // 平板版（橫向）
  XL(breakpoint: 1200); // 電腦版（桌面）

  final double breakpoint;

  const SizeLevel({required this.breakpoint});
}

/// 響應式佈局元件
class AutoLayout extends StatelessWidget {
  final Widget layoutS;
  final Widget? layoutM;
  final Widget? layoutL;
  final Widget? layoutXL;

  const AutoLayout({
    super.key,
    required this.layoutS,
    this.layoutM,
    this.layoutL,
    this.layoutXL,
  });

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    final sizeLevel = SizeLevel.values.firstWhere((e) => width < e.breakpoint);

    return switch (sizeLevel) {
      SizeLevel.S => layoutS,
      SizeLevel.M => layoutM ?? layoutS,
      SizeLevel.L => layoutL ?? layoutM ?? layoutS,
      SizeLevel.XL => layoutXL ?? layoutL ?? layoutM ?? layoutS,
    };
  }
}
