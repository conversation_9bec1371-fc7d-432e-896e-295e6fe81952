import 'package:flutter/material.dart';

typedef CellBuilder = Widget Function(int index);

/// 表格建構器，使用表格建構類似 GridView 的效果
class TableBuilder extends StatelessWidget {
  final int? itemCount;
  final int columnCount;
  final CellBuilder? cellBuilder;
  final TableBorder? border;
  final List<Widget>? children;

  const TableBuilder({
    super.key,
    this.itemCount,
    this.cellBuilder,
    required this.columnCount,
    this.border,
    this.children,
  });

  @override
  Widget build(BuildContext context) {
    final items = children ??
        List.generate(itemCount ?? 0, (index) {
          return cellBuilder?.call(index) ?? Container();
        });
    final rowCount = (items.length / columnCount).ceil();
    return Table(
      border: border,
      children: List.generate(rowCount, (rowIndex) {
        return TableRow(
          children: List.generate(columnCount, (colIndex) {
            final itemIndex = rowIndex * columnCount + colIndex;
            if (itemIndex >= items.length) return Container();
            return cellBuilder?.call(itemIndex) ?? Container();
          }),
        );
      }),
    );
  }
}
