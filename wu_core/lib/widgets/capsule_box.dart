import 'package:flutter/material.dart';

class CapsuleBox extends StatelessWidget {
  final Widget left;
  final Widget child;
  final Color leftColor;
  const CapsuleBox({
    super.key,
    required this.left,
    required this.child,
    this.leftColor = const Color.fromARGB(255, 230, 138, 247),
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      clipBehavior: Clip.antiAlias,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          AspectRatio(
            aspectRatio: 1,
            child: Container(
              alignment: Alignment.center,
              color: leftColor,
              child: left,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: child,
          ),
        ],
      ),
    );
  }
}
