import 'package:flutter/material.dart';
import 'package:wu_fortune/wu_fortune.dart';

/// 經典小六壬：大安、留連、速喜、赤口、小吉、空亡
/// 秘傳小六壬：大安、留連、速喜、赤口、小吉、空亡、病符、桃花、天德
/// 秘傳因按照八卦宮位安排，因此可以利用五行生剋強化解讀
/// 符號被克制時需要改善則去祭拜相對應神明
const XIAO_LIUREN = {
  "大安": {
    "順序": "1",
    "意義": "長期、緩慢、穩定",
    "五行": "木",
    "方位": "正東",
    "八卦": "震",
    "特性": "求安穩，大安最吉。求變化，大安不吉。",
    "神明": "三清",
    "吉凶": "吉",
  },
  "留連": {
    "順序": "2",
    "意義": "停止、反覆、複雜",
    "五行": "木",
    "方位": "西南",
    "八卦": "巽",
    "特性": "想挽留，留連最吉。否則都很噁心。",
    "神明": "文昌",
    "吉凶": "凶",
  },
  "速喜": {
    "順序": "3",
    "意義": "驚喜、快速、突然",
    "五行": "火",
    "方位": "正南",
    "八卦": "離",
    "特性": "意想不到的好事。",
    "神明": "雷祖",
    "吉凶": "吉",
  },
  "赤口": {
    "順序": "4",
    "意義": "爭鬥、凶惡、傷害",
    "五行": "金",
    "方位": "正西",
    "八卦": "兌",
    "特性": "吵架、打架、鬥爭、訴訟是非、肉體受傷（尤其赤口疊現）",
    "神明": "將帥",
    "吉凶": "凶",
  },
  "小吉": {
    "順序": "5",
    "意義": "起步、不多、尚可",
    "五行": "水",
    "方位": "正北",
    "八卦": "坎",
    "特性": "成中有缺、適合起步",
    "神明": "真武",
    "吉凶": "吉",
  },
  "空亡": {
    "順序": "6",
    "意義": "失去、虛偽、空想",
    "五行": "土",
    "方位": "內",
    "八卦": "中",
    "特性": "先得再失，尤忌金錢事。可多接觸玄學、哲學、心理學",
    "神明": "玉皇",
    "吉凶": "凶",
  },
  "病符": {
    "順序": "7",
    "意義": "病態、異常、治療",
    "五行": "金",
    "方位": "西南",
    "八卦": "乾",
    "特性": "先有病，才需要‘治’",
    "神明": "后土",
    "吉凶": "凶",
  },
  "桃花": {
    "順序": "8",
    "意義": "慾望、牽絆、異性",
    "五行": "土",
    "方位": "東北",
    "八卦": "艮",
    "特性": "人際關係，牽絆此事。姻緣好，其他差。",
    "神明": "城隍",
    "吉凶": "吉",
  },
  "天德": {
    "順序": "9",
    "意義": "貴人、上司、高遠",
    "五行": "金",
    "方位": "西北",
    "八卦": "坤",
    "特性": "求人辦事，靠人成事",
    "神明": "紫微",
    "吉凶": "吉",
  },
};

enum LiurenMode {
  classic,
  secret,
}

class XiaoLiuren {
  final LiurenMode mode;
  final List<String> keys;
  final Color colorGood;
  final Color colorBad;

  XiaoLiuren(this.mode, {this.colorGood = Colors.green, this.colorBad = Colors.red})
      : keys = switch (mode) {
          LiurenMode.classic => XIAO_LIUREN.keys.take(6).toList(),
          LiurenMode.secret => XIAO_LIUREN.keys.toList(),
        };

  List<String> liurens(List<int> numbers) {
    int pos = 0;
    final ret = <String>[];
    for (var i = 0; i < 3; i++) {
      pos += numbers[i] - 1;
      ret.add(keys[pos % keys.length]);
    }
    return ret;
  }

  ListTile liurenTile(String key) {
    final liuren = XIAO_LIUREN[key]!;
    final colorJx = liuren["吉凶"] == "吉" ? colorGood : colorBad;
    final colorWx = Wuxing.byName(liuren["五行"]!).color.withAlpha(100);
    return switch (mode) {
      LiurenMode.classic => ListTile(
          leading: CircleAvatar(backgroundColor: colorJx, child: Text(key)),
          title: Text(liuren["意義"]!),
          subtitle: Text(liuren["特性"]!),
        ),
      LiurenMode.secret => ListTile(
          leading: CircleAvatar(
            backgroundColor: colorWx,
            foregroundColor: colorJx,
            child: Text(key),
          ),
          title: Text("${liuren["八卦"]}${liuren["五行"]} ${liuren["意義"]}"),
          subtitle: Text(liuren["特性"]!),
        ),
    };
  }

  String liurenPrompt(String key) {
    final liuren = XIAO_LIUREN[key]!;
    return switch (mode) {
      LiurenMode.classic => "符號：$key。意義：${liuren["意義"]}。特性：${liuren["特性"]}。",
      LiurenMode.secret => "符號：$key。八卦：${liuren["八卦"]}。五行：${liuren["五行"]}。" //
          "意義：${liuren["意義"]}。特性：${liuren["特性"]}。神明：${liuren["神明"]}。",
    };
  }
}
